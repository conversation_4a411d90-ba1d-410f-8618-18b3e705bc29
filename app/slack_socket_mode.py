import logging
import traceback

from typing import Optional

import psycopg
from slack_sdk.socket_mode import SocketMode<PERSON>lient
from slack_sdk.socket_mode.request import SocketModeRequest
from slack_sdk.socket_mode.response import SocketModeResponse
from slack_sdk import WebClient

from .config import SLACK_TOKEN, SLACK_APP_TOKEN, ALEXBOT_DB_URL, PORTAL_DB_URL
from .utils import get_db_connection, get_robot_by_channel
from .visualization_requests import request_visualization
from .info_command import handle_info_command_logic
from .qa_command import handle_qa_command
from .support_summary_command import handle_support_summary_command
from .qa_versions_command import (
    handle_qa_versions_command,
    handle_qa_versions_update_command,
    create_software_versions_table_if_not_exists,
)

logger = logging.getLogger(__name__)


def handle_viz_command(
    slack_client: WebClient,
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    robot_id: Optional[str],
) -> str:
    """Handle /viz command. Returns response message."""
    try:
        # Determine robot ID
        if robot_id:
            # Robot was specified as argument
            logger.info(f"User @{user_name} requested visualization for robot {robot_id} in channel {channel_name}")
        else:
            # No robot specified, determine from channel
            robot_id = get_robot_by_channel(portal_conn, channel_name)
            if not robot_id:
                return (
                    f"❌ Error: No robot configured for channel {channel_name}. Please specify a robot: `/viz <robot>`"
                )
            logger.info(f"User @{user_name} requested visualization for channel {channel_name} (robot: {robot_id})")

        # Request visualization (bypass rate limit for /viz commands)
        success = request_visualization(
            alexbot_conn, robot_id, channel_name, requested_by=user_name, bypass_rate_limit=True
        )

        if success:
            return f"✅ Visualization requested for robot `{robot_id}`. You'll receive it in this channel shortly."
        else:
            return f"❌ Visualization request for robot `{robot_id}` failed. Please try again later."
    except Exception as e:
        logger.error(f"Error handling /viz command: {e}")
        return "❌ Error: Failed to process visualization request"


def handle_info_command(
    slack_client: WebClient,
    alexbot_conn: psycopg.Connection,
    portal_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    robot_serial: str,
) -> str:
    """Handle /info command. Returns response message for private reply or posts to channel."""
    try:
        # Use the extracted business logic
        success, message, _ = handle_info_command_logic(
            alexbot_conn, portal_conn, channel_name, user_name, robot_serial
        )

        if success:
            # Post successful result to channel
            slack_client.chat_postMessage(channel=channel_name, text=message)
            return ""  # Empty response for private reply
        else:
            # Return error message for private reply
            return message

    except Exception as e:
        logger.error(f"Error handling /info command: {e}")
        traceback.print_exc()
        return "❌ Error: Failed to process robot info request"


def handle_socket_mode_request(client: SocketModeClient, req: SocketModeRequest):
    """Handle incoming Socket Mode requests."""
    try:
        # Check if this is a slash command
        if req.type == "slash_commands":
            payload = req.payload

            # Check if this is the /viz command
            if payload.get("command") == "/viz":
                # Get necessary information
                channel_name = f"#{payload['channel_name']}"
                user_name = payload["user_name"]
                robot_id = payload.get("text", "")

                logger.info(f"Received /viz command from user @{user_name} in channel {channel_name}: {robot_id}")

                # Connect to databases
                alexbot_conn = get_db_connection(ALEXBOT_DB_URL)
                portal_conn = get_db_connection(PORTAL_DB_URL)

                try:
                    # Handle the command
                    response_text = handle_viz_command(
                        client.web_client, alexbot_conn, portal_conn, channel_name, user_name, robot_id
                    )

                    # Send response
                    response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": response_text})
                    client.send_socket_mode_response(response)

                finally:
                    alexbot_conn.close()
                    portal_conn.close()

            elif payload.get("command") == "/info":
                # Get necessary information
                channel_name = f"#{payload['channel_name']}"
                user_name = payload["user_name"]
                robot_serial = payload.get("text", "").strip()

                logger.info(f"Received /info command from user @{user_name} in channel {channel_name}: {robot_serial}")

                # Connect to databases
                alexbot_conn = get_db_connection(ALEXBOT_DB_URL)
                portal_conn = get_db_connection(PORTAL_DB_URL)

                try:
                    # Handle the command
                    response_text = handle_info_command(
                        client.web_client, alexbot_conn, portal_conn, channel_name, user_name, robot_serial
                    )

                    # Send response (empty payload if posted to channel, error message if private)
                    if response_text:
                        # Error case - send private error message
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": response_text})
                    else:
                        # Success case - send empty acknowledgment (message already posted to channel)
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={})
                    client.send_socket_mode_response(response)

                finally:
                    alexbot_conn.close()
                    portal_conn.close()

            elif payload.get("command") == "/qa":
                
                logger.info(f"payload: {payload}")
                
                # Get necessary information
                channel_name = f"#{payload['channel_name']}"
                user_name = payload["user_name"]
                command_text = payload.get("text", "").strip()
                channel_id = payload.get("channel_id")
                message_ts = payload.get("ts")

                logger.info(f"Received /qa command from user @{user_name} in channel {channel_name}: {command_text}")

                # Connect to databases
                portal_conn = get_db_connection(PORTAL_DB_URL)

                try:
                    # Handle the command
                    success, response_text = handle_qa_command(
                        portal_conn, channel_name, user_name, command_text, channel_id, message_ts
                    )

                    if success:
                        # Success case - post to channel
                        client.web_client.chat_postMessage(channel=channel_name, text=response_text)
                        # Send empty acknowledgment
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={})
                    else:
                        # Error case - send private error message
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": response_text})

                    client.send_socket_mode_response(response)

                finally:
                    portal_conn.close()

            elif payload.get("command") == "/support-summary":
                # Get necessary information
                channel_name = f"#{payload['channel_name']}"
                user_name = payload["user_name"]
                command_text = payload.get("text", "").strip()

                logger.info(f"Received /support-summary command from user @{user_name}")

                try:
                    # Handle the command (no database connection needed for this command)
                    success, response_messages = handle_support_summary_command(command_text)

                    if success:
                        # Success case - post all message chunks to channel
                        for message_chunk in response_messages:
                            client.web_client.chat_postMessage(channel=channel_name, text=message_chunk)
                        # Send empty acknowledgment
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={})
                    else:
                        # Error case - send private error message (should only be one message for errors)
                        error_text = response_messages[0] if response_messages else "❌ Unknown error"
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": error_text})

                    client.send_socket_mode_response(response)

                except Exception as e:
                    logger.error(f"Error handling /support-summary command: {e}")
                    traceback.print_exc()
                    error_response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": "❌ Error: Failed to generate support summary"})
                    client.send_socket_mode_response(error_response)

            elif payload.get("command") == "/qa-versions":
                # Get necessary information
                channel_name = f"#{payload['channel_name']}"
                user_name = payload["user_name"]

                logger.info(f"Received /qa-versions command from user @{user_name} in channel {channel_name}")

                # Connect to database
                alexbot_conn = get_db_connection(ALEXBOT_DB_URL)

                try:
                    # Ensure table exists
                    create_software_versions_table_if_not_exists(alexbot_conn)

                    # Handle the command
                    success, response_text = handle_qa_versions_command(alexbot_conn, user_name)

                    if success:
                        # Success case - post to channel
                        client.web_client.chat_postMessage(channel=channel_name, text=response_text)
                        # Send empty acknowledgment
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={})
                    else:
                        # Error case - send private error message
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": response_text})

                    client.send_socket_mode_response(response)

                finally:
                    alexbot_conn.close()

            elif payload.get("command") == "/qa-versions-update":
                # Get necessary information
                channel_name = f"#{payload['channel_name']}"
                user_name = payload["user_name"]
                command_text = payload.get("text", "").strip()

                logger.info(f"Received /qa-versions-update command from user @{user_name} in channel {channel_name}: {command_text}")

                # Connect to database
                alexbot_conn = get_db_connection(ALEXBOT_DB_URL)

                try:
                    # Ensure table exists
                    create_software_versions_table_if_not_exists(alexbot_conn)

                    # Handle the command
                    success, response_text = handle_qa_versions_update_command(alexbot_conn, user_name, command_text)

                    if success:
                        # Success case - post to channel
                        client.web_client.chat_postMessage(channel=channel_name, text=response_text)
                        # Send empty acknowledgment
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={})
                    else:
                        # Error case - send private error message
                        response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": response_text})

                    client.send_socket_mode_response(response)

                finally:
                    alexbot_conn.close()

            else:
                # Unknown command
                response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": "❌ Unknown command"})
                client.send_socket_mode_response(response)
        else:
            # Not a slash command
            response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": "❌ Invalid request type"})
            client.send_socket_mode_response(response)

    except Exception as e:
        logger.error(f"Error handling Socket Mode request: {e}")
        response = SocketModeResponse(envelope_id=req.envelope_id, payload={"text": "❌ Internal server error"})
        client.send_socket_mode_response(response)


def start_socket_mode_client():
    """Start the Socket Mode client."""
    try:
        # Create Socket Mode client
        client = SocketModeClient(app_token=SLACK_APP_TOKEN, web_client=WebClient(token=SLACK_TOKEN))

        # Set up request handler
        client.socket_mode_request_listeners.append(handle_socket_mode_request)

        # Start the client
        logger.info("Starting Slack Socket Mode client...")
        client.connect()

    except Exception as e:
        logger.error(f"Failed to start Socket Mode client: {e}")
        raise
