import logging
import traceback
from typing import <PERSON><PERSON>, List, Optional
import psycopg

logger = logging.getLogger(__name__)


def handle_qa_versions_command(
    alexbot_conn: psycopg.Connection,
    user_name: str,
) -> Tuple[bool, str]:
    """Handle /qa-versions command. Returns success status and response message."""
    try:
        # Get all versions where qa=true
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                SELECT id, version, status
                FROM software_versions
                WHERE qa = true
                ORDER BY version
                """
            )
            results = cursor.fetchall()

        if not results:
            return True, "No QA versions found."

        # Format the response
        response_lines = ["**QA Versions:**"]
        for row in results:
            version_id, version, status = row
            response_lines.append(f"• **{version}** (ID: {version_id}) - Status: {status}")

        response_text = "\n".join(response_lines)
        logger.info(f"User @{user_name} requested QA versions list")
        return True, response_text

    except Exception as e:
        logger.error(f"Error in handle_qa_versions_command: {e}")
        traceback.print_exc()
        return False, "Error: Failed to retrieve QA versions"


def handle_qa_versions_update_command(
    alexbot_conn: psycopg.Connection,
    user_name: str,
    command_text: str,
) -> Tuple[bool, str]:
    """Handle /qa-versions-update command. Returns success status and response message."""
    try:
        # Parse command arguments: [version] [qa] [status]
        if not command_text or not command_text.strip():
            return False, "Error: Please provide version, qa flag, and status: `/qa-versions-update <version> <true/false> <status>`"

        parts = command_text.strip().split()
        if len(parts) < 3:
            return False, "Error: Please provide all required parameters: `/qa-versions-update <version> <true/false> <status>`"

        version = parts[0]
        qa_str = parts[1].lower()
        status = " ".join(parts[2:])  # Allow status to contain spaces

        # Validate qa parameter
        if qa_str not in ["true", "false"]:
            return False, "Error: QA flag must be 'true' or 'false'"

        qa_bool = qa_str == "true"

        # Check if version exists, if not create it
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                "SELECT id FROM software_versions WHERE version = %s",
                (version,)
            )
            existing = cursor.fetchone()

            if existing:
                # Update existing version
                cursor.execute(
                    """
                    UPDATE software_versions
                    SET qa = %s, status = %s
                    WHERE version = %s
                    """,
                    (qa_bool, status, version)
                )
                action = "updated"
            else:
                # Create new version with generated ID
                import uuid
                version_id = str(uuid.uuid4())
                cursor.execute(
                    """
                    INSERT INTO software_versions (id, version, qa, status)
                    VALUES (%s, %s, %s, %s)
                    """,
                    (version_id, version, qa_bool, status)
                )
                action = "created"

            alexbot_conn.commit()

        # Format success message
        qa_status = "enabled" if qa_bool else "disabled"
        success_msg = f"Version **{version}** {action} successfully!\nQA: {qa_status}\nStatus: {status}\nUpdated by: @{user_name}"

        logger.info(f"User @{user_name} {action} version {version} with qa={qa_bool}, status='{status}'")
        return True, success_msg

    except Exception as e:
        logger.error(f"Error in handle_qa_versions_update_command: {e}")
        traceback.print_exc()
        return False, "Error: Failed to update QA version"


def create_software_versions_table_if_not_exists(alexbot_conn: psycopg.Connection) -> None:
    """Create the software_versions table if it doesn't exist."""
    try:
        with alexbot_conn.cursor() as cursor:
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS software_versions (
                    id TEXT PRIMARY KEY,
                    version TEXT NOT NULL,
                    qa BOOLEAN NOT NULL DEFAULT FALSE,
                    status TEXT NOT NULL
                )
                """
            )
            alexbot_conn.commit()
            logger.info("Ensured software_versions table exists")
    except Exception as e:
        logger.error(f"Error creating software_versions table: {e}")
        raise
