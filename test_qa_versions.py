#!/usr/bin/env python3
"""
Test script for QA versions commands.
This script tests the qa_versions_command module functions.
"""

import sys
import os
import tempfile
import sqlite3
from unittest.mock import Mock

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from qa_versions_command import (
    handle_qa_versions_command,
    handle_qa_versions_update_command,
    create_software_versions_table_if_not_exists,
)


class MockConnection:
    """Mock database connection for testing."""
    
    def __init__(self):
        # Create an in-memory SQLite database for testing
        self.sqlite_conn = sqlite3.connect(':memory:')
        self.sqlite_conn.execute('''
            CREATE TABLE software_versions (
                id TEXT PRIMARY KEY,
                version TEXT NOT NULL,
                qa BOOLEAN NOT NULL DEFAULT FALSE,
                status TEXT NOT NULL
            )
        ''')
        self.sqlite_conn.commit()
    
    def cursor(self):
        return MockCursor(self.sqlite_conn)
    
    def commit(self):
        self.sqlite_conn.commit()
    
    def close(self):
        self.sqlite_conn.close()


class MockCursor:
    """Mock database cursor for testing."""
    
    def __init__(self, sqlite_conn):
        self.sqlite_conn = sqlite_conn
        self.cursor = sqlite_conn.cursor()
    
    def execute(self, query, params=None):
        # Convert PostgreSQL-style placeholders to SQLite-style
        sqlite_query = query.replace('%s', '?')
        if params:
            return self.cursor.execute(sqlite_query, params)
        else:
            return self.cursor.execute(sqlite_query)
    
    def fetchall(self):
        return self.cursor.fetchall()
    
    def fetchone(self):
        return self.cursor.fetchone()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass


def test_qa_versions_commands():
    """Test the QA versions commands."""
    print("Testing QA versions commands...")
    
    # Create mock connection
    conn = MockConnection()
    
    try:
        # Test 1: Empty list initially
        print("\n1. Testing empty QA versions list...")
        success, message = handle_qa_versions_command(conn, "testuser")
        print(f"Success: {success}, Message: {message}")
        assert success == True
        assert "No QA versions found" in message
        
        # Test 2: Create a new version
        print("\n2. Testing version creation...")
        success, message = handle_qa_versions_update_command(conn, "testuser", "v1.0.0 true In Testing")
        print(f"Success: {success}, Message: {message}")
        assert success == True
        assert "created successfully" in message
        
        # Test 3: List versions (should show the created version)
        print("\n3. Testing QA versions list with data...")
        success, message = handle_qa_versions_command(conn, "testuser")
        print(f"Success: {success}, Message: {message}")
        assert success == True
        assert "v1.0.0" in message
        assert "In Testing" in message
        
        # Test 4: Update existing version
        print("\n4. Testing version update...")
        success, message = handle_qa_versions_update_command(conn, "testuser", "v1.0.0 false Completed")
        print(f"Success: {success}, Message: {message}")
        assert success == True
        assert "updated successfully" in message
        
        # Test 5: Create another version with qa=true
        print("\n5. Testing another version creation...")
        success, message = handle_qa_versions_update_command(conn, "testuser", "v2.0.0 true Ready for QA")
        print(f"Success: {success}, Message: {message}")
        assert success == True
        assert "created successfully" in message
        
        # Test 6: List QA versions (should only show v2.0.0 since v1.0.0 has qa=false)
        print("\n6. Testing QA versions list (should only show qa=true versions)...")
        success, message = handle_qa_versions_command(conn, "testuser")
        print(f"Success: {success}, Message: {message}")
        assert success == True
        assert "v2.0.0" in message
        assert "Ready for QA" in message
        assert "v1.0.0" not in message  # Should not appear since qa=false
        
        # Test 7: Error cases
        print("\n7. Testing error cases...")
        
        # Missing parameters
        success, message = handle_qa_versions_update_command(conn, "testuser", "")
        print(f"Empty command - Success: {success}, Message: {message}")
        assert success == False
        assert "Please provide" in message
        
        # Invalid qa parameter
        success, message = handle_qa_versions_update_command(conn, "testuser", "v3.0.0 maybe Testing")
        print(f"Invalid qa param - Success: {success}, Message: {message}")
        assert success == False
        assert "must be 'true' or 'false'" in message
        
        print("\n✅ All tests passed!")
        
    finally:
        conn.close()


if __name__ == "__main__":
    test_qa_versions_commands()
